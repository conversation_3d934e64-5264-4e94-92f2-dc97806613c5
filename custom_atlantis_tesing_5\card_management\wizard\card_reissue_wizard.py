# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError, UserError


class CardReissueWizard(models.TransientModel):
    _name = 'card.reissue.wizard'
    _description = 'Card Reissue Wizard'

    # Old Card Information
    old_card_id = fields.Many2one(
        'resort.card',
        string='البطاقة القديمة',
        required=True,
        readonly=True,
        help='The old card to be replaced'
    )
    
    customer_name = fields.Char(
        string='اسم العميل',
        required=True,
        help='Customer name (can be updated)'
    )
    
    phone = fields.Char(
        string='رقم الهاتف',
        help='Customer phone number (can be updated)'
    )
    
    current_balance = fields.Float(
        string='الرصيد الحالي',
        readonly=True,
        digits=(16, 2),
        help='Current balance to transfer to new card'
    )
    
    # New Card Information
    new_barcode = fields.Char(
        string='رقم البطاقة الجديد',
        readonly=True,
        help='New card number (auto-generated)'
    )
    
    reason = fields.Selection([
        ('lost', 'بطاقة مفقودة'),
        ('damaged', 'بطاقة تالفة'),
        ('other', 'أخرى'),
    ], string='سبب إعادة الإصدار', required=True, default='lost')
    
    notes = fields.Text(
        string='ملاحظات',
        help='Additional notes about the reissue'
    )
    
    # Computed fields
    old_card_barcode = fields.Char(
        string='باركود البطاقة القديمة',
        related='old_card_id.barcode',
        readonly=True
    )

    @api.model
    def default_get(self, fields_list):
        """Set default values from context and auto-generate new card number"""
        defaults = super().default_get(fields_list)

        # Auto-generate new card number
        if 'new_barcode' in fields_list:
            sequence = self.env['ir.sequence'].next_by_code('resort.card.barcode')
            if not sequence:
                # Fallback if sequence doesn't exist yet (99 prefix + 12 digits)
                card_count = self.env['resort.card'].search_count([])
                sequence = f"99{str(card_count + 1).zfill(12)}"
            defaults['new_barcode'] = sequence

        return defaults
    
    # Note: No barcode validation needed since card numbers are auto-generated and unique

    def action_reissue_card(self):
        """Process the card reissue"""
        self.ensure_one()
        
        old_card = self.old_card_id
        
        # Validate old card status
        if not old_card.card_status_id or old_card.card_status_id.code != 'lost':
            raise UserError(_('Can only reissue lost cards. Current status: %s') % (
                old_card.card_status_id.name if old_card.card_status_id else 'No status'
            ))
        
        # Get current real-time balance
        current_balance = old_card.get_real_time_balance()
        
        # Create new card with same customer
        new_card_vals = {
            'name': self.customer_name,
            'phone': self.phone,
            'barcode': self.new_barcode,
            'partner_id': old_card.partner_id.id,  # Link to same customer
        }
        
        new_card = self.env['resort.card'].create(new_card_vals)

        # Simply archive the old card (no need for complex "replaced" status)
        old_card.write({
            'active': False,  # Archive old card
        })
        
        # Create transaction records for the reissue
        if current_balance != 0:
            # Record balance transfer from old card
            self.env['card.transaction'].create_transaction(
                card_id=old_card.id,
                transaction_type='adjustment',
                amount=-current_balance,
                description=f'Balance transferred to new card {self.new_barcode} - {dict(self._fields["reason"].selection)[self.reason]}'
            )
            
            # Record balance transfer to new card
            self.env['card.transaction'].create_transaction(
                card_id=new_card.id,
                transaction_type='adjustment',
                amount=current_balance,
                description=f'Balance transferred from old card {old_card.barcode} - {dict(self._fields["reason"].selection)[self.reason]}'
            )
        
        # Create reissue log record
        self.env['card.reissue.log'].create({
            'old_card_id': old_card.id,
            'new_card_id': new_card.id,
            'reason': self.reason,
            'notes': self.notes,
            'balance_transferred': current_balance,
            'processed_by': self.env.user.id,
        })
        
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Card Reissued Successfully'),
                'message': _('New card %s has been issued. Balance %.2f transferred from old card %s.') % (
                    self.new_barcode, current_balance, old_card.barcode
                ),
                'type': 'success',
                'next': {
                    'type': 'ir.actions.act_window',
                    'res_model': 'resort.card',
                    'res_id': new_card.id,
                    'view_mode': 'form',
                    'views': [(False, 'form')],
                    'target': 'current',
                }
            }
        }


class CardReissueLog(models.Model):
    _name = 'card.reissue.log'
    _description = 'Card Reissue Log'
    _order = 'create_date desc'
    _rec_name = 'display_name'

    old_card_id = fields.Many2one(
        'resort.card',
        string='البطاقة القديمة',
        required=True,
        help='The old card that was replaced'
    )
    
    new_card_id = fields.Many2one(
        'resort.card',
        string='البطاقة الجديدة',
        required=True,
        help='The new card that was issued'
    )
    
    reason = fields.Selection([
        ('lost', 'بطاقة مفقودة'),
        ('damaged', 'بطاقة تالفة'),
        ('other', 'أخرى'),
    ], string='سبب إعادة الإصدار', required=True)
    
    notes = fields.Text(
        string='ملاحظات',
        help='Additional notes about the reissue'
    )
    
    balance_transferred = fields.Float(
        string='الرصيد المحول',
        digits=(16, 2),
        help='Balance transferred from old to new card'
    )
    
    processed_by = fields.Many2one(
        'res.users',
        string='تم بواسطة',
        required=True,
        help='User who processed the reissue'
    )
    
    create_date = fields.Datetime(
        string='تاريخ إعادة الإصدار',
        readonly=True
    )
    
    # Computed fields
    display_name = fields.Char(
        string='اسم العرض',
        compute='_compute_display_name',
        store=True
    )
    
    old_card_barcode = fields.Char(
        string='باركود البطاقة القديمة',
        related='old_card_id.barcode',
        readonly=True
    )
    
    new_card_barcode = fields.Char(
        string='باركود البطاقة الجديدة',
        related='new_card_id.barcode',
        readonly=True
    )
    
    @api.depends('old_card_barcode', 'new_card_barcode')
    def _compute_display_name(self):
        """Compute display name for reissue log"""
        for log in self:
            old_barcode = log.old_card_barcode or 'Old'
            new_barcode = log.new_card_barcode or 'New'
            log.display_name = f"Reissue: {old_barcode} → {new_barcode}"
