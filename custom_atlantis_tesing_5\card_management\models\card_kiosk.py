# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError


class CardKiosk(models.TransientModel):
    _name = 'card.kiosk'
    _description = 'Card Balance Kiosk'

    # Input Fields
    card_barcode = fields.Char(
        string='Scan Card',
        required=True,
        help='Scan your card barcode to check balance'
    )
    
    # Display Fields
    card_found = fields.Boolean(
        string='Card Found',
        default=False,
        help='Technical field to track if card was found'
    )
    card_balance = fields.Float(
        string='Available Balance',
        digits=(16, 0),
        help='Your available card balance'
    )
    currency_id = fields.Many2one(
        'res.currency',
        string='Currency',
        default=lambda self: self.env.company.currency_id,
        help='Currency for balance display'
    )
    card_status = fields.Char(
        string='Card Status',
        help='Current status of your card'
    )
    last_transaction_date = fields.Datetime(
        string='Last Transaction',
        help='Date of your last transaction'
    )
    
    # Display State
    display_state = fields.Selection([
        ('scan', 'Scan Card'),
        ('show_balance', 'Show Balance'),
        ('card_not_found', 'Card Not Found'),
        ('card_inactive', 'Card Inactive'),
    ], default='scan', string='Display State')

    @api.onchange('card_barcode')
    def _onchange_card_barcode(self):
        """Check card when barcode is scanned"""
        if self.card_barcode:
            # Find the card
            card = self.env['resort.card'].search([
                ('barcode', '=', self.card_barcode),
                ('active', '=', True)
            ], limit=1)
            
            if card:
                # Check if card is active
                if card.card_status_id.code == 'active':
                    # Card found and active - show real-time balance
                    # Use real-time balance that considers open sessions
                    if card.partner_id:
                        real_time_balance = card.partner_id.get_real_time_balance()
                        customer_balance = round(abs(real_time_balance))
                    else:
                        customer_balance = 0
                    
                    self.update({
                        'card_found': True,
                        'card_balance': customer_balance,
                        'card_status': card.card_status_id.name,
                        'display_state': 'show_balance',
                        'last_transaction_date': self._get_last_transaction_date(card.partner_id),
                    })
                else:
                    # Card found but not active
                    self.update({
                        'card_found': False,
                        'card_balance': 0.0,
                        'card_status': card.card_status_id.name,
                        'display_state': 'card_inactive',
                        'last_transaction_date': False,
                    })
            else:
                # Card not found
                self.update({
                    'card_found': False,
                    'card_balance': 0.0,
                    'card_status': 'Not Found',
                    'display_state': 'card_not_found',
                    'last_transaction_date': False,
                })
        else:
            # Reset to scan state
            self.update({
                'card_found': False,
                'card_balance': 0.0,
                'card_status': '',
                'display_state': 'scan',
                'last_transaction_date': False,
            })

    def _get_last_transaction_date(self, partner):
        """Get the last transaction date for the customer"""
        if not partner:
            return False
        
        # Find the most recent payment for this customer
        last_payment = self.env['account.payment'].search([
            ('partner_id', '=', partner.id),
            ('state', '=', 'posted')
        ], order='date desc', limit=1)
        
        return last_payment.date if last_payment else False

    def action_check_balance(self):
        """Manually trigger balance check"""
        # Force trigger the onchange method
        self._onchange_card_barcode()
        return True

    def action_scan_new_card(self):
        """Reset to scan a new card"""
        self.update({
            'card_barcode': '',
            'card_found': False,
            'card_balance': 0.0,
            'card_status': '',
            'display_state': 'scan',
            'last_transaction_date': False,
        })

        return {
            'type': 'ir.actions.act_window',
            'res_model': 'card.kiosk',
            'res_id': self.id,
            'view_mode': 'form',
            'target': 'new',
            'context': {'form_view_initial_mode': 'edit'}
        }



    def action_close_kiosk(self):
        """Close the kiosk"""
        return {'type': 'ir.actions.act_window_close'}
