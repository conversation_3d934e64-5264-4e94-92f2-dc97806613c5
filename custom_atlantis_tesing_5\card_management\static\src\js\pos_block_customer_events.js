odoo.define('card_management.pos_block_customer_events', function (require) {
'use strict';

var core = require('web.core');
var { Gui } = require('point_of_sale.Gui');
var Registries = require('point_of_sale.Registries');

// Note: In Odoo 15, we use component extension instead of Gui.include
// This functionality is handled by other components, so we'll just add DOM blocking

// Block customer selection at the document level
document.addEventListener('DOMContentLoaded', function() {
    // Block clicks on customer-related elements
    document.addEventListener('click', function(event) {
        var target = event.target;
        
        // Check if click is on customer selection elements
        if (target.classList.contains('set-customer') ||
            target.classList.contains('client-name') ||
            target.classList.contains('customer-button') ||
            target.closest('.set-customer') ||
            target.closest('.client-line')) {
            
            // Check if this is a PIN-validated customer display
            var orderWidget = target.closest('.order-widget');
            if (orderWidget) {
                var currentOrder = window.posmodel && window.posmodel.get_order();
                var currentCustomer = currentOrder && currentOrder.get_client();
                
                if (currentCustomer && currentCustomer._pinValidated) {
                    // Allow display of validated customer info
                    return;
                }
            }
            
            // Block the click
            event.preventDefault();
            event.stopPropagation();
            event.stopImmediatePropagation();
            
            // Show security message
            if (window.posmodel && window.posmodel.gui) {
                window.posmodel.gui.show_popup('error', {
                    title: 'Customer Selection Disabled',
                    body: 'For security reasons, customers can only be selected by scanning their card and entering PIN.\n\nPlease scan the customer\'s card to proceed.',
                });
            }
            
            return false;
        }
    }, true); // Use capture phase to catch events early
});

});
