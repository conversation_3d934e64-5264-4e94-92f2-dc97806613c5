<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <!-- Customer Form View - Add Card Management Section -->
    <record id="view_partner_form_card_management" model="ir.ui.view">
        <field name="name">res.partner.form.card.management</field>
        <field name="model">res.partner</field>
        <field name="inherit_id" ref="base.view_partner_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='barcode']" position="after">
                <field name="has_card" string="لديه بطاقة" invisible="1"/>
                <field name="card_status_id" string="حالة البطاقة" attrs="{'invisible': [('has_card', '=', False)]}"/>
                <field name="card_assigned_date" string="تاريخ تعيين البطاقة" attrs="{'invisible': [('has_card', '=', False)]}" readonly="1"/>
                <field name="card_balance" string="رصيد البطاقة" attrs="{'invisible': [('has_card', '=', False)]}" readonly="1"/>
                <field name="card_notes" string="ملاحظات البطاقة" attrs="{'invisible': [('has_card', '=', False)]}"/>
            </xpath>
        </field>
    </record>

    <!-- Customer Tree View - Add Card Status -->
    <record id="view_partner_tree_card_management" model="ir.ui.view">
        <field name="name">res.partner.tree.card.management</field>
        <field name="model">res.partner</field>
        <field name="inherit_id" ref="base.view_partner_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='phone']" position="after">
                <field name="has_card" string="لديه بطاقة"/>
                <field name="card_status_id" string="حالة البطاقة" optional="hide"/>
                <field name="card_balance" string="رصيد البطاقة" optional="hide"/>
            </xpath>
        </field>
    </record>

    <!-- Add Card Management Actions to Customer Form -->
    <record id="view_partner_form_card_actions" model="ir.ui.view">
        <field name="name">res.partner.form.card.actions</field>
        <field name="model">res.partner</field>
        <field name="inherit_id" ref="base.view_partner_form"/>
        <field name="arch" type="xml">
            <xpath expr="//div[@name='button_box']" position="inside">
                <!-- Assign Card Button -->
                <button name="action_assign_card" 
                        type="object" 
                        class="oe_stat_button" 
                        icon="fa-credit-card"
                        attrs="{'invisible': [('has_card', '=', True)]}">
                    <div class="o_field_widget o_stat_info">
                        <span class="o_stat_text">تعيين</span>
                        <span class="o_stat_text">بطاقة</span>
                    </div>
                </button>

                <!-- Card Status Button -->
                <button name="action_deactivate_card"
                        type="object"
                        class="oe_stat_button"
                        icon="fa-credit-card"
                        attrs="{'invisible': [('has_card', '=', False)]}">
                    <field name="card_status_id" widget="statinfo" string="حالة البطاقة"/>
                </button>

                <!-- Top Up Card Button -->
                <button name="action_topup_card"
                        type="object"
                        class="oe_stat_button"
                        icon="fa-money"
                        attrs="{'invisible': [('has_card', '=', False)]}">
                    <field name="card_balance" widget="statinfo" string="رصيد البطاقة"/>
                </button>

                <!-- Test Balance Button (for debugging) -->
                <button name="action_test_balance_calculation"
                        type="object"
                        class="oe_stat_button"
                        icon="fa-calculator"
                        attrs="{'invisible': [('has_card', '=', False)]}">
                    <div class="o_field_widget o_stat_info">
                        <span class="o_stat_text">Test</span>
                        <span class="o_stat_text">Balance</span>
                    </div>
                </button>
            </xpath>
        </field>
    </record>



    <!-- Search View - Add Card Filters -->
    <record id="view_res_partner_filter_card_management" model="ir.ui.view">
        <field name="name">res.partner.search.card.management</field>
        <field name="model">res.partner</field>
        <field name="inherit_id" ref="base.view_res_partner_filter"/>
        <field name="arch" type="xml">
            <xpath expr="//filter[@name='supplier']" position="after">
                <separator/>
                <filter string="لديه بطاقة" name="has_card" domain="[('has_card', '=', True)]"/>
                <filter string="البطاقات النشطة" name="active_cards" domain="[('card_status_id.code', '=', 'active')]"/>
                <filter string="البطاقات غير النشطة" name="inactive_cards" domain="[('card_status_id.code', '=', 'inactive')]"/>
                <filter string="البطاقات المفقودة" name="lost_cards" domain="[('card_status_id.code', '=', 'lost')]"/>
            </xpath>
            <xpath expr="//group" position="inside">
                <filter string="حالة البطاقة" name="group_card_status" context="{'group_by': 'card_status_id'}"/>
            </xpath>
        </field>
    </record>

</odoo>
