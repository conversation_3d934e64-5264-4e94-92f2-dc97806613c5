odoo.define('card_management.pos_customer_barcode', function (require) {
'use strict';

var models = require('point_of_sale.models');
var ProductScreen = require('point_of_sale.ProductScreen');
var Registries = require('point_of_sale.Registries');
var { Gui } = require('point_of_sale.Gui');

// Extend POS models to load customer barcode data
models.load_fields('res.partner', ['barcode', 'credit', 'total_due', 'debit']);

// Extend ProductScreen to handle customer card scanning and balance validation
const PosCardProductScreen = (ProductScreen) =>
    class extends ProductScreen {
        constructor() {
            super(...arguments);
        }

        // Override the client barcode action with PIN validation
        async _barcodeClientAction(code) {
            try {
                // Step 1: Try to find customer locally first
                let partner = this.env.pos.db.get_partner_by_barcode(code.code);

                if (partner) {
                    // Step 2: Customer found locally, refresh their data from server
                    console.log('Customer found locally, refreshing data...');
                    const freshPartner = await this._fetchCustomerByBarcode(code.code);

                    if (freshPartner) {
                        // Update local database with fresh data
                        this._updatePartnerInPOS(freshPartner);
                        partner = freshPartner;
                    }
                } else {
                    // Step 3: Customer not found locally, fetch from server (new card)
                    console.log('Customer not found locally, fetching from server...');
                    const freshPartner = await this._fetchCustomerByBarcode(code.code);

                    if (freshPartner) {
                        // Add new customer to local POS database
                        this.env.pos.db.add_partners([freshPartner]);
                        partner = freshPartner;
                        console.log('New customer added to POS:', freshPartner.name);
                    }
                }

                if (partner) {
                    // Step 4: VALIDATE PIN FIRST before setting customer
                    const pinResult = await this._validateCustomerPin(partner);
                    if (!pinResult.success) {
                        // PIN validation failed - don't set customer
                        return false;
                    }

                    // Step 5: PIN validated - mark customer as validated and set
                    partner._pinValidated = true; // Mark as PIN validated
                    if (this.currentOrder.get_client() !== partner) {
                        this.currentOrder.set_client(partner);
                        this.currentOrder.updatePricelist(partner);
                    }

                    // PIN validation was successful - customer details already shown in _validateCustomerPin
                    return true;
                }

                // Step 5: Card truly doesn't exist in system
                this.showPopup('ErrorPopup', {
                    title: 'البطاقة غير موجودة',
                    body: 'هذه البطاقة غير مسجلة في النظام. يرجى التحقق من رقم البطاقة أو تسجيل البطاقة أولاً.',
                });
                return false;

            } catch (error) {
                console.error('Error in smart refresh:', error);
                console.error('Error details:', error.message, error.data);

                // Fallback to local data if server error
                const localPartner = this.env.pos.db.get_partner_by_barcode(code.code);
                console.log('Fallback to local partner:', localPartner);

                if (localPartner) {
                    // Even in offline mode, validate PIN first
                    const pinResult = await this._validateCustomerPin(localPartner);
                    if (!pinResult.success) {
                        return false;
                    }

                    localPartner._pinValidated = true; // Mark as PIN validated
                    this.currentOrder.set_client(localPartner);
                    // PIN validation success popup already shown in _validateCustomerPin
                    return true;
                }

                // Show network error
                this.showPopup('ErrorPopup', {
                    title: 'خطأ في الاتصال',
                    body: 'غير قادر على التحقق من البطاقة. يرجى التحقق من الاتصال والمحاولة مرة أخرى.',
                });
                return false;
            }
        }

        // Override the payment button click to check balance
        async _onClickPay() {
            // First check: Must have a customer selected
            if (!this.currentOrder.get_client()) {
                this.showPopup('ErrorPopup', {
                    title: 'العميل مطلوب',
                    body: 'يجب اختيار عميل قبل إتمام عملية الدفع.\nلا يمكن إجراء أي دفع بدون تحديد العميل.',
                });
                return; // Prevent navigation to payment screen
            }

            // Check if customer has a card and sufficient balance
            const customer = this.currentOrder.get_client();

            if (customer && customer.barcode) {
                // Customer has a resort card and PIN was already validated during barcode scan
                // Now check real-time balance
                const availableBalance = await this._getRealTimeBalance(customer);
                const orderTotal = this.currentOrder.get_total_with_tax();

                if (availableBalance < orderTotal) {
                    // Insufficient balance - show warning and prevent payment
                    const formattedAvailable = this.env.pos.format_currency(availableBalance);
                    const formattedRequired = this.env.pos.format_currency(orderTotal);

                    this.showPopup('ErrorPopup', {
                        title: 'رصيد غير كافٍ',
                        body: 'الرصيد المتاح: ' + formattedAvailable + '\n' +
                              'المبلغ المطلوب: ' + formattedRequired + '\n\n' +
                              'يرجى زيارة الكاشير لشحن بطاقتك.',
                    });
                    return; // Prevent navigation to payment screen
                }
            }

            // If no card customer or sufficient balance, proceed normally
            super._onClickPay();
        }

        async _validateCustomerPin(customer) {
            try {
                // Get card information for this customer
                const cardData = await this.env.services.rpc({
                    model: 'resort.card',
                    method: 'search_read',
                    args: [[['partner_id', '=', customer.id], ['active', '=', true]]],
                    kwargs: { fields: ['id', 'barcode', 'name', 'phone'], limit: 1 }
                });

                if (!cardData || cardData.length === 0) {
                    this.showPopup('ErrorPopup', {
                        title: 'البطاقة غير موجودة',
                        body: 'لم يتم العثور على بطاقة نشطة لهذا العميل.',
                    });
                    return { success: false };
                }

                const card = cardData[0];

                // No card blocking - proceed directly to PIN validation

                // Show PIN input popup - NO customer info revealed until PIN is validated
                const pinResult = await this.showPopup('TextInputPopup', {
                    title: 'أمان البطاقة - أدخل الرقم السري',
                    body: `البطاقة: ${card.barcode}\n\nأدخل الرقم السري المكون من 4 أرقام (آخر 4 أرقام من رقم الهاتف):`,
                    placeholder: 'أدخل الرقم السري...',
                });

                if (pinResult.confirmed && pinResult.payload) {
                    // Validate the entered PIN
                    const enteredPin = pinResult.payload.trim();

                    if (enteredPin.length !== 4 || !/^\d{4}$/.test(enteredPin)) {
                        this.showPopup('ErrorPopup', {
                            title: 'رقم سري غير صحيح',
                            body: 'يرجى إدخال رقم سري مكون من 4 أرقام.',
                        });
                        return { success: false };
                    }

                    // Validate PIN with server
                    const validationResult = await this.env.services.rpc({
                        model: 'resort.card',
                        method: 'validate_card_pin',
                        args: [card.id, enteredPin],
                    });

                    if (validationResult.success) {
                        // PIN validated successfully - NOW we can show customer details with real-time balance
                        const realTimeBalance = await this._getRealTimeBalance(customer);
                        const formattedBalance = this.env.pos.format_currency(realTimeBalance);

                        this.showPopup('ConfirmPopup', {
                            title: 'تم التحقق من الرقم السري بنجاح',
                            body: `العميل: ${customer.name}\n` +
                                  `الرصيد المتاح: ${formattedBalance}`,
                        });

                        // Store real-time balance in customer object for later use
                        customer._realTimeBalance = realTimeBalance;

                        return { success: true };
                    } else {
                        // PIN validation failed - offer manager override
                        const managerResult = await this.showPopup('ConfirmPopup', {
                            title: 'رقم سري خاطئ',
                            body: validationResult.message || 'رقم سري خاطئ. هل تريد استخدام تجاوز المدير؟',
                            confirmText: 'تجاوز المدير',
                            cancelText: 'إلغاء',
                        });

                        if (managerResult.confirmed) {
                            // Show manager barcode input
                            const managerInput = await this.showPopup('TextInputPopup', {
                                title: 'تجاوز المدير',
                                body: 'امسح أو أدخل باركود المدير:',
                                placeholder: 'باركود المدير...',
                            });

                            if (managerInput.confirmed && managerInput.payload) {
                                // For now, accept any non-empty barcode as manager override
                                // In production, you'd validate against actual manager barcodes
                                const realTimeBalance = await this._getRealTimeBalance(customer);
                                const formattedBalance = this.env.pos.format_currency(realTimeBalance);

                                this.showPopup('ConfirmPopup', {
                                    title: 'تم اعتماد تجاوز المدير',
                                    body: `تم استخدام تجاوز المدير.\n\n` +
                                          `العميل: ${customer.name}\n` +
                                          `الرصيد المتاح: ${formattedBalance}`,
                                });

                                // Store real-time balance in customer object for later use
                                customer._realTimeBalance = realTimeBalance;

                                // Manager override approved (no logging needed)

                                return { success: true };
                            }
                        }

                        return { success: false };
                    }
                } else {
                    // User cancelled
                    return { success: false };
                }

            } catch (error) {
                console.error('PIN validation error:', error);
                this.showPopup('ErrorPopup', {
                    title: 'Validation Error',
                    body: 'Unable to validate card PIN. Please try again.',
                });
                return { success: false };
            }
        }

        // Fetch customer data from server by barcode
        async _fetchCustomerByBarcode(barcode) {
            try {
                const result = await this.env.services.rpc({
                    model: 'res.partner',
                    method: 'search_read',
                    args: [[['barcode', '=', barcode]]],
                    kwargs: {
                        fields: ['id', 'name', 'barcode', 'credit', 'phone', 'email', 'vat', 'lang', 'category_id', 'partner_latitude', 'partner_longitude', 'property_product_pricelist'],
                        limit: 1,
                    },
                });

                console.log('Server response for barcode', barcode, ':', result);
                return result.length > 0 ? result[0] : null;
            } catch (error) {
                console.error('Failed to fetch customer by barcode:', error);
                throw error;
            }
        }

        // Update partner data in POS database
        _updatePartnerInPOS(partner) {
            // Update the partner in the POS database
            this.env.pos.db.partner_by_id[partner.id] = partner;

            // Update barcode index
            if (partner.barcode) {
                this.env.pos.db.partner_by_barcode = this.env.pos.db.partner_by_barcode || {};
                this.env.pos.db.partner_by_barcode[partner.barcode] = partner;
            }

            // Update search index if it exists
            if (this.env.pos.db.partner_search_string) {
                const search_string = this.env.pos.db._partner_search_string(partner);
                this.env.pos.db.partner_search_string[partner.id] = search_string;
            }
        }

        // Find customer by card barcode (legacy method)
        _findCustomerByCardBarcode(barcode) {
            const partners = this.env.pos.db.get_partners_sorted(1000);
            for (let partner of partners) {
                if (partner.barcode === barcode) {
                    return partner;
                }
            }
            return null;
        }

        // Get real-time balance for customer
        async _getRealTimeBalance(customer) {
            if (!customer || !customer.barcode) {
                return Math.abs(customer.credit || 0);
            }

            try {
                const result = await this.env.services.rpc({
                    model: 'res.partner',
                    method: 'get_customer_real_time_balance',
                    args: [customer.id],
                });

                if (result.success) {
                    return Math.abs(result.balance || 0);
                } else {
                    console.warn('Failed to get real-time balance:', result.message);
                    return Math.abs(customer.credit || 0);
                }
            } catch (error) {
                console.error('Error fetching real-time balance:', error);
                // Fallback to stored credit
                return Math.abs(customer.credit || 0);
            }
        }
    };

Registries.Component.extend(ProductScreen, PosCardProductScreen);

return {
    PosCardProductScreen,
};

});
