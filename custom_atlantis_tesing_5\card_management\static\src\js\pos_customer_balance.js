odoo.define('card_management.pos_customer_balance', function (require) {
'use strict';

var PosComponent = require('point_of_sale.PosComponent');
var ProductScreen = require('point_of_sale.ProductScreen');
var ActionpadWidget = require('point_of_sale.ActionpadWidget');
var Registries = require('point_of_sale.Registries');
var { useListener } = require('web.custom_hooks');

// Customer Balance Widget
class CustomerBalanceWidget extends PosComponent {
    constructor() {
        super(...arguments);
        useListener('order-changed', this._onOrderChanged);
        useListener('client-changed', this._onClientChanged);
    }

    async _onOrderChanged() {
        await this.updateCustomerRealTimeBalance();
        this.render();
    }

    async _onClientChanged() {
        await this.updateCustomerRealTimeBalance();
        this.render();
    }
    
    get currentOrder() {
        return this.env.pos.get_order();
    }
    
    get customer() {
        return this.currentOrder ? this.currentOrder.get_client() : null;
    }
    
    get customerBalance() {
        if (this.customer && this.customer.credit !== undefined) {
            // Use real-time balance if available, otherwise fall back to credit
            if (this.customer._realTimeBalance !== undefined) {
                return Math.abs(this.customer._realTimeBalance || 0);
            }
            // Convert negative credit to positive balance for display
            return Math.abs(this.customer.credit || 0);
        }
        return 0;
    }

    async updateCustomerRealTimeBalance() {
        if (!this.customer || !this.customer.barcode) return;

        try {
            const result = await this.env.services.rpc({
                model: 'res.partner',
                method: 'get_customer_real_time_balance',
                args: [this.customer.id],
            });

            if (result.success) {
                // Store real-time balance in customer object
                this.customer._realTimeBalance = result.balance;
                // Trigger re-render
                this.render();
            }
        } catch (error) {
            console.error('Failed to fetch real-time balance:', error);
        }
    }
    
    get formattedBalance() {
        return this.env.pos.format_currency(this.customerBalance);
    }
    
    get hasCustomer() {
        return !!this.customer;
    }
    
    get customerName() {
        return this.customer ? this.customer.name : '';
    }
}

CustomerBalanceWidget.template = 'CustomerBalanceWidget';

// Extend ProductScreen to include customer balance
const PosResCardProductScreen = (ProductScreen) =>
    class extends ProductScreen {
        constructor() {
            super(...arguments);
        }

        // Override to trigger balance update when customer changes
        _onClickCustomer() {
            super._onClickCustomer();
            // The balance widget will automatically update via useListener
        }

        // Override to trigger balance update when products are added
        async _clickProduct(event) {
            const result = await super._clickProduct(event);
            // Trigger balance update for card customers
            this._triggerBalanceUpdate();
            return result;
        }

        // Override to trigger balance update when orderlines change
        async _updateOrderline(event) {
            const result = await super._updateOrderline(event);
            // Trigger balance update for card customers
            this._triggerBalanceUpdate();
            return result;
        }

        _triggerBalanceUpdate() {
            const customer = this.currentOrder?.get_client();
            if (customer && customer.barcode) {
                // Trigger order-changed event to update balance displays
                this.trigger('order-changed');
            }
        }
    };

// Extend ActionpadWidget to show customer balance
const PosCardActionpadWidget = (ActionpadWidget) =>
    class extends ActionpadWidget {
        constructor() {
            super(...arguments);
            useListener('order-changed', this._onOrderChanged);
            useListener('client-changed', this._onClientChanged);
        }

        async _onOrderChanged() {
            await this._updateClientRealTimeBalance();
            this.render();
        }

        async _onClientChanged() {
            await this._updateClientRealTimeBalance();
            this.render();
        }

        get client() {
            const originalClient = super.client;
            if (originalClient && originalClient.credit !== undefined) {
                // Use real-time balance if available, otherwise fall back to credit
                let balance;
                if (originalClient._realTimeBalance !== undefined) {
                    balance = Math.abs(originalClient._realTimeBalance || 0);
                } else {
                    balance = Math.abs(originalClient.credit || 0);
                }

                const balanceText = `الرصيد : ${Math.round(balance)}`;

                // Create a completely new object using JSON to avoid any reference issues
                try {
                    const newClient = JSON.parse(JSON.stringify(originalClient));
                    newClient.name = `${originalClient.name}\n${balanceText}`;
                    return newClient;
                } catch (error) {
                    // If JSON parsing fails, return original client
                    console.warn('Failed to create client copy:', error);
                    return originalClient;
                }
            }
            return originalClient;
        }

        async _updateClientRealTimeBalance() {
            const client = this.env.pos.get_order()?.get_client();
            if (client && client.barcode) {
                try {
                    const result = await this.env.services.rpc({
                        model: 'res.partner',
                        method: 'get_customer_real_time_balance',
                        args: [client.id],
                    });

                    if (result.success) {
                        client._realTimeBalance = result.balance;
                        this.render();
                    }
                } catch (error) {
                    console.error('Failed to update real-time balance:', error);
                }
            }
        }
    };

Registries.Component.extend(ProductScreen, PosResCardProductScreen);
Registries.Component.extend(ActionpadWidget, PosCardActionpadWidget);
Registries.Component.add(CustomerBalanceWidget);

return {
    CustomerBalanceWidget,
    PosResCardProductScreen,
    PosCardActionpadWidget,
};

});
