# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError, UserError


class ResPartner(models.Model):
    _inherit = 'res.partner'

    # Card Management Fields
    card_status_id = fields.Many2one(
        'card.status',
        string='حالة البطاقة',
        help='Current status of the customer card'
    )
    card_assigned_date = fields.Datetime(
        string='تاريخ تعيين البطاقة',
        help='Date when the card was assigned to this customer'
    )
    card_notes = fields.Text(
        string='ملاحظات البطاقة',
        help='Additional notes about the card'
    )
    has_card = fields.Bo<PERSON>an(
        string='لديه بطاقة',
        compute='_compute_has_card',
        store=True,
        help='Indicates if customer has a card assigned'
    )
    card_balance = fields.Monetary(
        string='رصيد البطاقة',
        compute='_compute_card_balance',
        help='Current card balance (customer credit)'
    )

    @api.depends('barcode')
    def _compute_has_card(self):
        """Compute if customer has a card based on barcode field"""
        for partner in self:
            partner.has_card = bool(partner.barcode)

    def _compute_card_balance(self):
        """Compute card balance using real-time balance"""
        for partner in self:
            if partner.barcode:
                # For card customers, use real-time balance
                partner.card_balance = partner.get_real_time_balance()
            else:
                # For non-card customers, use standard credit
                partner.card_balance = partner.credit

    def get_real_time_balance(self):
        """
        Get real-time balance considering open POS sessions
        This is the main function to get accurate balance for card customers
        """
        self.ensure_one()
        if not self.barcode:
            # Not a card customer, return standard credit
            return self.credit

        # Start with the posted balance (from closed sessions)
        base_balance = self.credit or 0.0

        # Find all open POS sessions that might have transactions for this customer
        open_sessions = self.env['pos.session'].search([
            ('state', 'in', ['opening_control', 'opened'])
        ])

        # Debug: Log open sessions
        import logging
        _logger = logging.getLogger(__name__)
        _logger.info(f"Checking real-time balance for {self.name} (ID: {self.id})")
        _logger.info(f"Base balance (credit): {base_balance}")
        _logger.info(f"Found {len(open_sessions)} open sessions: {[s.name for s in open_sessions]}")

        # Calculate pending transactions from open sessions
        pending_amount = 0.0

        for session in open_sessions:
            # Get all orders for this customer in this open session
            session_orders = self.env['pos.order'].search([
                ('session_id', '=', session.id),
                ('partner_id', '=', self.id),
                ('state', 'in', ['draft', 'paid'])  # Include both draft and paid orders
            ])

            # Debug: Log what we found
            if session_orders:
                import logging
                _logger = logging.getLogger(__name__)
                _logger.info(f"Found {len(session_orders)} orders for {self.name} in session {session.name}")
                for order in session_orders:
                    _logger.info(f"  Order {order.name}: amount={order.amount_total}, state={order.state}")

            for order in session_orders:
                if order.amount_total > 0:
                    # Purchase - customer owes money, this reduces their available credit
                    # Example: balance -300, order +20 = -300 + 20 = -280 (less credit available)
                    pending_amount += order.amount_total
                elif order.amount_total < 0:
                    # Refund - customer gets money back, this increases their available credit
                    # Example: balance -300, refund -20 = -300 - 20 = -320 (more credit available)
                    pending_amount += order.amount_total  # order.amount_total is already negative

        # Calculate final real-time balance
        real_time_balance = base_balance + pending_amount

        # Debug logging (remove in production)
        if pending_amount != 0:
            import logging
            _logger = logging.getLogger(__name__)
            _logger.info(f"Real-time balance calculation for {self.name}: "
                        f"base_balance={base_balance}, pending_amount={pending_amount}, "
                        f"final_balance={real_time_balance}")

        return real_time_balance

    @api.model
    def get_customer_real_time_balance(self, customer_id):
        """
        API method to get real-time balance from POS
        """
        customer = self.browse(customer_id)
        if not customer.exists():
            return {'success': False, 'message': 'Customer not found'}

        try:
            balance = customer.get_real_time_balance()
            return {
                'success': True,
                'balance': balance,
                'formatted_balance': customer.env['res.currency'].browse(customer.env.company.currency_id.id).format(abs(balance))
            }
        except Exception as e:
            return {'success': False, 'message': str(e)}

    def test_balance_calculation(self):
        """Test method to verify balance calculation - remove in production"""
        self.ensure_one()
        if not self.barcode:
            return "Not a card customer"

        base_balance = self.credit or 0.0

        # Find open sessions
        open_sessions = self.env['pos.session'].search([
            ('state', 'in', ['opening_control', 'opened'])
        ])

        pending_amount = 0.0
        session_details = []

        for session in open_sessions:
            session_orders = self.env['pos.order'].search([
                ('session_id', '=', session.id),
                ('partner_id', '=', self.id),
                ('state', 'in', ['draft', 'paid'])
            ])

            session_total = 0.0
            for order in session_orders:
                session_total += order.amount_total
                pending_amount += order.amount_total

            if session_total != 0:
                session_details.append(f"Session {session.id}: {session_total}")

        real_time_balance = base_balance + pending_amount

        result = f"""
Balance Calculation Test for {self.name}:
- Base Balance (credit): {base_balance}
- Open Sessions: {len(open_sessions)}
- Session Details: {session_details}
- Total Pending: {pending_amount}
- Real-time Balance: {real_time_balance}
- Display Balance: {abs(real_time_balance)}
        """

        return result

    def action_test_balance_calculation(self):
        """Action to test balance calculation"""
        result = self.test_balance_calculation()
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': 'Balance Calculation Test',
                'message': result,
                'type': 'info',
                'sticky': True,
            }
        }

    @api.model
    def create(self, vals):
        """Override create to set default card status when barcode is assigned"""
        partner = super(ResPartner, self).create(vals)
        if partner.barcode and not partner.card_status_id:
            default_status = self.env['card.status'].get_default_active_status()
            if default_status:
                partner.card_status_id = default_status.id
                partner.card_assigned_date = fields.Datetime.now()
        return partner

    def write(self, vals):
        """Override write to handle card status changes"""
        result = super(ResPartner, self).write(vals)
        
        # If barcode is being assigned for the first time
        if 'barcode' in vals:
            for partner in self:
                if partner.barcode and not partner.card_status_id:
                    default_status = self.env['card.status'].get_default_active_status()
                    if default_status:
                        partner.card_status_id = default_status.id
                        partner.card_assigned_date = fields.Datetime.now()
                elif not partner.barcode:
                    # If barcode is removed, set status to inactive
                    inactive_status = self.env['card.status'].get_inactive_status()
                    if inactive_status:
                        partner.card_status_id = inactive_status.id
        
        return result

    def action_assign_card(self):
        """Action to assign a new card to customer"""
        self.ensure_one()
        if self.barcode:
            raise UserError(_('Customer already has a card assigned.'))
        
        return {
            'name': _('Assign Card'),
            'type': 'ir.actions.act_window',
            'res_model': 'res.partner',
            'res_id': self.id,
            'view_mode': 'form',
            'target': 'current',
            'context': {'form_view_initial_mode': 'edit'}
        }

    def action_deactivate_card(self):
        """Action to deactivate customer card"""
        self.ensure_one()
        if not self.barcode:
            raise UserError(_('Customer does not have a card assigned.'))
        
        inactive_status = self.env['card.status'].get_inactive_status()
        if inactive_status:
            self.card_status_id = inactive_status.id
        return True

    def action_report_lost_card(self):
        """Action to report card as lost"""
        self.ensure_one()
        if not self.barcode:
            raise UserError(_('Customer does not have a card assigned.'))
        
        lost_status = self.env['card.status'].get_lost_status()
        if lost_status:
            self.card_status_id = lost_status.id
        return True

    def action_report_stolen_card(self):
        """Action to report card as stolen"""
        self.ensure_one()
        if not self.barcode:
            raise UserError(_('Customer does not have a card assigned.'))
        
        stolen_status = self.env['card.status'].get_stolen_status()
        if stolen_status:
            self.card_status_id = stolen_status.id
        return True

    def action_reactivate_card(self):
        """Action to reactivate customer card"""
        self.ensure_one()
        if not self.barcode:
            raise UserError(_('Customer does not have a card assigned.'))
        
        active_status = self.env['card.status'].get_default_active_status()
        if active_status:
            self.card_status_id = active_status.id
        return True

    def action_topup_card(self):
        """Action to top up customer card (register payment)"""
        self.ensure_one()
        if not self.barcode:
            raise UserError(_('Customer does not have a card assigned.'))

        return {
            'name': _('Top Up Card - Register Payment'),
            'type': 'ir.actions.act_window',
            'res_model': 'account.payment',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_partner_id': self.id,
                'default_payment_type': 'inbound',
                'default_partner_type': 'customer',
                'default_amount': 0.0,
            }
        }

    @api.constrains('barcode')
    def _check_barcode_format(self):
        """Validate barcode format (numeric only)"""
        for partner in self:
            if partner.barcode and not partner.barcode.isdigit():
                raise ValidationError(_('Card barcode must contain only numeric characters.'))
