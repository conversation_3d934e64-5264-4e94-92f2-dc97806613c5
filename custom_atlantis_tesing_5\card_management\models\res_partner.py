# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError, UserError


class ResPartner(models.Model):
    _inherit = 'res.partner'

    # Card Management Fields
    card_status_id = fields.Many2one(
        'card.status',
        string='حالة البطاقة',
        help='Current status of the customer card'
    )
    card_assigned_date = fields.Datetime(
        string='تاريخ تعيين البطاقة',
        help='Date when the card was assigned to this customer'
    )
    card_notes = fields.Text(
        string='ملاحظات البطاقة',
        help='Additional notes about the card'
    )
    has_card = fields.Bo<PERSON>an(
        string='لديه بطاقة',
        compute='_compute_has_card',
        store=True,
        help='Indicates if customer has a card assigned'
    )
    card_balance = fields.Monetary(
        string='رصيد البطاقة',
        compute='_compute_card_balance',
        help='Current card balance (customer credit)'
    )

    @api.depends('barcode')
    def _compute_has_card(self):
        """Compute if customer has a card based on barcode field"""
        for partner in self:
            partner.has_card = bool(partner.barcode)

    def _compute_card_balance(self):
        """Compute card balance using real-time balance"""
        for partner in self:
            if partner.barcode:
                # For card customers, use real-time balance
                partner.card_balance = partner.get_real_time_balance()
            else:
                # For non-card customers, use standard credit
                partner.card_balance = partner.credit

    def get_real_time_balance(self):
        """
        Get real-time balance considering open POS sessions
        This is the main function to get accurate balance for card customers
        """
        self.ensure_one()
        if not self.barcode:
            # Not a card customer, return standard credit
            return self.credit

        # Start with the posted balance (from closed sessions)
        base_balance = self.credit or 0.0

        # Find all open POS sessions that might have transactions for this customer
        open_sessions = self.env['pos.session'].search([
            ('state', 'in', ['opening_control', 'opened'])
        ])

        # Calculate pending transactions from open sessions
        pending_amount = 0.0

        for session in open_sessions:
            # Get all orders for this customer in this open session
            session_orders = self.env['pos.order'].search([
                ('session_id', '=', session.id),
                ('partner_id', '=', self.id),
                ('state', 'in', ['draft', 'paid'])  # Include both draft and paid orders
            ])

            for order in session_orders:
                if order.amount_total > 0:
                    # Purchase - customer spends money, reduces their available balance
                    # This should decrease the credit (make it less positive or more negative)
                    pending_amount -= order.amount_total
                elif order.amount_total < 0:
                    # Refund - customer gets money back, increases their available balance
                    # This should increase the credit (make it more positive or less negative)
                    pending_amount += abs(order.amount_total)

        # Calculate final real-time balance
        real_time_balance = base_balance + pending_amount

        # Debug logging (remove in production)
        if pending_amount != 0:
            import logging
            _logger = logging.getLogger(__name__)
            _logger.info(f"Real-time balance calculation for {self.name}: "
                        f"base_balance={base_balance}, pending_amount={pending_amount}, "
                        f"final_balance={real_time_balance}")

        return real_time_balance

    @api.model
    def get_customer_real_time_balance(self, customer_id):
        """
        API method to get real-time balance from POS
        """
        customer = self.browse(customer_id)
        if not customer.exists():
            return {'success': False, 'message': 'Customer not found'}

        try:
            balance = customer.get_real_time_balance()
            return {
                'success': True,
                'balance': balance,
                'formatted_balance': customer.env['res.currency'].browse(customer.env.company.currency_id.id).format(abs(balance))
            }
        except Exception as e:
            return {'success': False, 'message': str(e)}

    @api.model
    def create(self, vals):
        """Override create to set default card status when barcode is assigned"""
        partner = super(ResPartner, self).create(vals)
        if partner.barcode and not partner.card_status_id:
            default_status = self.env['card.status'].get_default_active_status()
            if default_status:
                partner.card_status_id = default_status.id
                partner.card_assigned_date = fields.Datetime.now()
        return partner

    def write(self, vals):
        """Override write to handle card status changes"""
        result = super(ResPartner, self).write(vals)
        
        # If barcode is being assigned for the first time
        if 'barcode' in vals:
            for partner in self:
                if partner.barcode and not partner.card_status_id:
                    default_status = self.env['card.status'].get_default_active_status()
                    if default_status:
                        partner.card_status_id = default_status.id
                        partner.card_assigned_date = fields.Datetime.now()
                elif not partner.barcode:
                    # If barcode is removed, set status to inactive
                    inactive_status = self.env['card.status'].get_inactive_status()
                    if inactive_status:
                        partner.card_status_id = inactive_status.id
        
        return result

    def action_assign_card(self):
        """Action to assign a new card to customer"""
        self.ensure_one()
        if self.barcode:
            raise UserError(_('Customer already has a card assigned.'))
        
        return {
            'name': _('Assign Card'),
            'type': 'ir.actions.act_window',
            'res_model': 'res.partner',
            'res_id': self.id,
            'view_mode': 'form',
            'target': 'current',
            'context': {'form_view_initial_mode': 'edit'}
        }

    def action_deactivate_card(self):
        """Action to deactivate customer card"""
        self.ensure_one()
        if not self.barcode:
            raise UserError(_('Customer does not have a card assigned.'))
        
        inactive_status = self.env['card.status'].get_inactive_status()
        if inactive_status:
            self.card_status_id = inactive_status.id
        return True

    def action_report_lost_card(self):
        """Action to report card as lost"""
        self.ensure_one()
        if not self.barcode:
            raise UserError(_('Customer does not have a card assigned.'))
        
        lost_status = self.env['card.status'].get_lost_status()
        if lost_status:
            self.card_status_id = lost_status.id
        return True

    def action_report_stolen_card(self):
        """Action to report card as stolen"""
        self.ensure_one()
        if not self.barcode:
            raise UserError(_('Customer does not have a card assigned.'))
        
        stolen_status = self.env['card.status'].get_stolen_status()
        if stolen_status:
            self.card_status_id = stolen_status.id
        return True

    def action_reactivate_card(self):
        """Action to reactivate customer card"""
        self.ensure_one()
        if not self.barcode:
            raise UserError(_('Customer does not have a card assigned.'))
        
        active_status = self.env['card.status'].get_default_active_status()
        if active_status:
            self.card_status_id = active_status.id
        return True

    def action_topup_card(self):
        """Action to top up customer card (register payment)"""
        self.ensure_one()
        if not self.barcode:
            raise UserError(_('Customer does not have a card assigned.'))

        return {
            'name': _('Top Up Card - Register Payment'),
            'type': 'ir.actions.act_window',
            'res_model': 'account.payment',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_partner_id': self.id,
                'default_payment_type': 'inbound',
                'default_partner_type': 'customer',
                'default_amount': 0.0,
            }
        }

    @api.constrains('barcode')
    def _check_barcode_format(self):
        """Validate barcode format (numeric only)"""
        for partner in self:
            if partner.barcode and not partner.barcode.isdigit():
                raise ValidationError(_('Card barcode must contain only numeric characters.'))
